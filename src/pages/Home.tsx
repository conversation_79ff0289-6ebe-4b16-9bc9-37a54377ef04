import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  Target,
  BarChart3,
  Zap,
  Activity,
  DollarSign,
  Bot,
  Settings,
  Bell,
  Search,
  Plus
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

interface UserAgent {
  id: string;
  name: string;
  description: string;
  returnPercentage: number;
  winRate: number;
  totalTrades: number;
  isActive: boolean;
  bestBacktest?: {
    returnPercentage: number;
    winRate: number;
    totalTrades: number;
    period: string;
  };
}

interface WatchlistItem {
  id: string;
  symbol: string;
  name: string;
  price: number;
  changePercent: number;
}



interface ProgressStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  completed: boolean;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress } = useGamification();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('User');

  // Enhanced mock data for premium dashboard
  const mockWatchlist: WatchlistItem[] = [
    { id: '1', symbol: 'AAPL', name: 'Apple Inc.', price: 185.42, changePercent: 2.1 },
    { id: '2', symbol: 'TSLA', name: 'Tesla Inc.', price: 248.50, changePercent: -1.8 },
    { id: '3', symbol: 'NVDA', name: 'NVIDIA Corp.', price: 875.30, changePercent: 4.2 },
    { id: '4', symbol: 'MSFT', name: 'Microsoft Corp.', price: 378.85, changePercent: 1.5 },
    { id: '5', symbol: 'GOOGL', name: 'Alphabet Inc.', price: 142.65, changePercent: -0.8 },
    { id: '6', symbol: 'META', name: 'Meta Platforms', price: 342.90, changePercent: 5.7 }
  ];

  const mockAgents: UserAgent[] = [
    {
      id: '1',
      name: 'Momentum Hunter',
      description: 'Identifies strong momentum stocks with technical analysis',
      returnPercentage: 24.5,
      winRate: 72,
      totalTrades: 45,
      isActive: true,
      bestBacktest: { returnPercentage: 24.5, winRate: 72, totalTrades: 45, period: '3M' }
    },
    {
      id: '2',
      name: 'Value Seeker',
      description: 'Finds undervalued opportunities using fundamental analysis',
      returnPercentage: 18.2,
      winRate: 68,
      totalTrades: 32,
      isActive: false,
      bestBacktest: { returnPercentage: 18.2, winRate: 68, totalTrades: 32, period: '6M' }
    },
    {
      id: '3',
      name: 'Breakout Trader',
      description: 'Captures breakout patterns with volume confirmation',
      returnPercentage: 31.8,
      winRate: 65,
      totalTrades: 28,
      isActive: true,
      bestBacktest: { returnPercentage: 31.8, winRate: 65, totalTrades: 28, period: '2M' }
    }
  ];

  const mockEarnings = {
    totalEarnings: 12847.50,
    salesCount: 23,
    monthlyGrowth: 18.4,
    topPerformer: 'Momentum Hunter'
  };

  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load agent statistics
      const { data: agents } = await supabase
        .from('agents')
        .select('id, created_at')
        .eq('user_id', user.id);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Progress steps for get started journey
  const progressSteps: ProgressStep[] = [
    {
      id: 'create-agent',
      title: 'Create Agent',
      description: 'Build your first AI trading agent',
      icon: <Zap className="w-4 h-4" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Scan',
      description: 'Discover trading opportunities',
      icon: <Target className="w-4 h-4" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Backtest Strategy',
      description: 'Test against historical data',
      icon: <BarChart3 className="w-4 h-4" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'discover-agents',
      title: 'Explore Marketplace',
      description: 'Discover community agents',
      icon: <Activity className="w-4 h-4" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    }
  ];

  const completedSteps = progressSteps.filter(step => step.completed).length;

  // Load user name
  useEffect(() => {
    const loadUserName = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.user_metadata?.full_name) {
          const firstName = user.user_metadata.full_name.split(' ')[0];
          setUserName(firstName);
        }
      } catch (error) {
        console.error('Error loading user name:', error);
      }
    };
    loadUserName();
  }, []);



  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }











  // Premium Earnings Card for Bento Layout
  const renderPremiumEarningsCard = () => (
    <div className="relative h-full flex flex-col justify-between">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 bg-gradient-to-br from-green-500/20 to-green-400/10 rounded-lg flex items-center justify-center border border-green-500/20 shadow-inner">
          <DollarSign className="w-4 h-4 text-green-400" />
        </div>
        <div>
          <h3 className="text-sm font-semibold text-white">Earnings</h3>
          <p className="text-white/50 text-xs">Marketplace revenue</p>
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <div className="text-2xl font-bold text-green-300">${mockEarnings.totalEarnings.toLocaleString()}</div>
          <div className="text-green-400 text-xs">+{mockEarnings.monthlyGrowth}% this month</div>
        </div>

        <div className="grid grid-cols-2 gap-2 text-center">
          <div className="bg-white/[0.02] rounded-lg p-2 border border-white/[0.05]">
            <div className="text-sm font-semibold text-white">{mockEarnings.salesCount}</div>
            <div className="text-white/50 text-xs">Sales</div>
          </div>
          <div className="bg-white/[0.02] rounded-lg p-2 border border-white/[0.05]">
            <div className="text-sm font-semibold text-green-300">{mockEarnings.topPerformer}</div>
            <div className="text-white/50 text-xs">Top Agent</div>
          </div>
        </div>
      </div>

      <button
        onClick={() => navigate('/marketplace')}
        className="w-full mt-3 px-3 py-2 bg-gradient-to-r from-green-500/10 to-green-400/5 border border-green-500/20 rounded-lg text-green-300 hover:text-green-200 hover:border-green-400/30 text-xs font-medium transition-all duration-200 shadow-inner"
      >
        View Marketplace
      </button>
    </div>
  );

  // Premium Watchlist Card for Bento Layout
  const renderPremiumWatchlistCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 bg-gradient-to-br from-white/[0.08] to-white/[0.04] rounded-lg flex items-center justify-center border border-white/[0.12] shadow-inner">
          <TrendingUp className="w-4 h-4 text-white/80" />
        </div>
        <div>
          <h3 className="text-sm font-semibold text-white">Watchlist</h3>
          <p className="text-white/50 text-xs">Market movers</p>
        </div>
      </div>

      <div className="flex-1 space-y-2">
        {mockWatchlist.slice(0, 3).map((item) => (
          <div key={item.id} className="relative bg-white/[0.02] backdrop-blur-sm rounded-lg border border-white/[0.05] hover:border-white/[0.08] transition-all duration-200 cursor-pointer overflow-hidden shadow-inner">
            <div className="p-3">
              <div className="flex items-center justify-between mb-2">
                <div>
                  <div className="font-semibold text-white text-sm">{item.symbol}</div>
                  <div className="text-white/40 text-xs">${item.price.toFixed(2)}</div>
                </div>
                <div className="text-right">
                  <div className={`text-sm font-medium ${
                    item.changePercent >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(1)}%
                  </div>
                </div>
              </div>

              {/* Inline Chart */}
              <div className="h-8 w-full">
                <svg width="100%" height="100%" viewBox="0 0 120 32" preserveAspectRatio="none" className="opacity-50">
                  <defs>
                    <linearGradient id={`gradient-${item.id}`} x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" stopColor={item.changePercent >= 0 ? '#6b7280' : '#6b7280'} stopOpacity="0.1"/>
                      <stop offset="100%" stopColor={item.changePercent >= 0 ? '#6b7280' : '#6b7280'} stopOpacity="0"/>
                    </linearGradient>
                  </defs>

                  <path
                    d={`M0,16 ${Array.from({length: 20}, (_, i) => {
                      const x = (i / 19) * 120;
                      const baseY = 16;
                      const trend = item.changePercent >= 0 ? -1 : 1;
                      const noise = (Math.random() - 0.5) * 6;
                      const trendEffect = (i / 19) * trend * 6;
                      const y = baseY + trendEffect + noise;
                      return `L${x},${Math.max(2, Math.min(30, y))}`;
                    }).join(' ')} L120,32 L0,32 Z`}
                    fill={`url(#gradient-${item.id})`}
                  />

                  <path
                    d={`M0,16 ${Array.from({length: 20}, (_, i) => {
                      const x = (i / 19) * 120;
                      const baseY = 16;
                      const trend = item.changePercent >= 0 ? -1 : 1;
                      const noise = (Math.random() - 0.5) * 6;
                      const trendEffect = (i / 19) * trend * 6;
                      const y = baseY + trendEffect + noise;
                      return `L${x},${Math.max(2, Math.min(30, y))}`;
                    }).join(' ')}`}
                    fill="none"
                    stroke="#6b7280"
                    strokeWidth="1"
                    opacity="0.6"
                  />
                </svg>
              </div>
            </div>
          </div>
        ))}
      </div>

      <button
        onClick={() => navigate('/stock-screener')}
        className="w-full mt-3 bg-gradient-to-r from-white/[0.05] to-white/[0.02] border border-white/[0.08] text-white/80 font-medium py-2 px-3 rounded-lg hover:border-white/[0.15] hover:bg-white/[0.08] transition-all duration-300 text-xs shadow-inner"
      >
        View All
      </button>
    </div>
  );

  // Premium Agents Card for Bento Layout
  const renderPremiumAgentsCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-white/[0.08] to-white/[0.04] rounded-xl flex items-center justify-center border border-white/[0.10] shadow-inner">
            <Bot className="w-5 h-5 text-white/80" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">Your Agents</h3>
            <p className="text-white/50 text-sm">Trading strategies</p>
          </div>
        </div>
        <button
          onClick={() => navigate('/builder')}
          className="bg-gradient-to-r from-white/[0.08] to-white/[0.04] border border-white/[0.12] text-white text-xs font-medium py-2 px-3 rounded-lg hover:bg-white/[0.12] transition-all duration-200 shadow-inner"
        >
          <Plus className="w-3 h-3 inline mr-1" />
          Create
        </button>
      </div>

      <div className="flex-1 space-y-3">
        {mockAgents.slice(0, 3).map((agent) => (
          <div
            key={agent.id}
            className="p-4 bg-white/[0.02] backdrop-blur-sm rounded-xl border border-white/[0.05] hover:border-white/[0.10] cursor-pointer transition-all duration-200 shadow-inner"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className={`w-8 h-8 rounded-lg flex items-center justify-center border ${
                  agent.isActive
                    ? 'bg-white/[0.08] border-white/[0.15] shadow-inner'
                    : 'bg-white/[0.04] border-white/[0.08]'
                }`}>
                  <Bot className="w-4 h-4 text-white/70" />
                </div>
                <div>
                  <div className="font-medium text-white text-sm">{agent.name}</div>
                  <div className="text-white/50 text-xs">{agent.description}</div>
                </div>
              </div>
              <div className={`w-2 h-2 rounded-full ${
                agent.isActive ? 'bg-white/60 shadow-lg shadow-white/20' : 'bg-white/20'
              }`} />
            </div>

            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="bg-white/[0.02] rounded-lg p-2 border border-white/[0.05]">
                <div className={`text-sm font-semibold ${
                  agent.returnPercentage >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {agent.returnPercentage >= 0 ? '+' : ''}{agent.returnPercentage}%
                </div>
                <div className="text-white/40 text-xs">Return</div>
              </div>
              <div className="bg-white/[0.02] rounded-lg p-2 border border-white/[0.05]">
                <div className="text-sm font-semibold text-white/80">{agent.winRate}%</div>
                <div className="text-white/40 text-xs">Win Rate</div>
              </div>
              <div className="bg-white/[0.02] rounded-lg p-2 border border-white/[0.05]">
                <div className="text-sm font-semibold text-white/80">{agent.totalTrades}</div>
                <div className="text-white/40 text-xs">Trades</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {mockAgents.length === 0 && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Bot className="w-12 h-12 text-white/30 mx-auto mb-3" />
            <p className="text-white/60 text-sm mb-4">No agents created yet</p>
            <button
              onClick={() => navigate('/builder')}
              className="bg-gradient-to-r from-white/[0.08] to-white/[0.04] border border-white/[0.12] text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-white/[0.12] transition-all duration-200"
            >
              Create Agent
            </button>
          </div>
        </div>
      )}
    </div>
  );



  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      {/* Premium Dashboard Header */}
      <div className="px-8 py-6 border-b border-white/[0.08] bg-gradient-to-br from-[#0A0A0A] via-[#0F0F0F] to-[#0A0A0A] relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-green-500/3 via-transparent to-green-400/3"></div>
        <div className="relative z-10">
          {/* Top Navigation Bar */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500/20 to-green-400/10 rounded-lg flex items-center justify-center border border-green-500/20">
                  <BarChart3 className="w-4 h-4 text-green-400" />
                </div>
                <span className="text-lg font-semibold text-white">Trading HQ</span>
              </div>

              {/* Quick Navigation */}
              <div className="flex items-center gap-1 bg-white/[0.03] backdrop-blur-sm border border-white/[0.08] rounded-lg p-1">
                <button
                  onClick={() => navigate('/scanner')}
                  className="px-3 py-1.5 text-sm text-white/70 hover:text-white hover:bg-white/[0.05] rounded-md transition-all duration-200"
                >
                  Scanner
                </button>
                <button
                  onClick={() => navigate('/agent-builder')}
                  className="px-3 py-1.5 text-sm text-white/70 hover:text-white hover:bg-white/[0.05] rounded-md transition-all duration-200"
                >
                  Builder
                </button>
                <button
                  onClick={() => navigate('/backtesting')}
                  className="px-3 py-1.5 text-sm text-white/70 hover:text-white hover:bg-white/[0.05] rounded-md transition-all duration-200"
                >
                  Backtest
                </button>
                <button
                  onClick={() => navigate('/marketplace')}
                  className="px-3 py-1.5 text-sm text-white/70 hover:text-white hover:bg-white/[0.05] rounded-md transition-all duration-200"
                >
                  Marketplace
                </button>
              </div>
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center gap-3">
              <button className="w-9 h-9 bg-white/[0.03] backdrop-blur-sm border border-white/[0.08] rounded-lg flex items-center justify-center hover:bg-white/[0.05] transition-all duration-200">
                <Search className="w-4 h-4 text-white/70" />
              </button>
              <button className="w-9 h-9 bg-white/[0.03] backdrop-blur-sm border border-white/[0.08] rounded-lg flex items-center justify-center hover:bg-white/[0.05] transition-all duration-200">
                <Bell className="w-4 h-4 text-white/70" />
              </button>
              <button
                onClick={() => navigate('/settings')}
                className="w-9 h-9 bg-white/[0.03] backdrop-blur-sm border border-white/[0.08] rounded-lg flex items-center justify-center hover:bg-white/[0.05] transition-all duration-200"
              >
                <Settings className="w-4 h-4 text-white/70" />
              </button>
            </div>
          </div>

          {/* Welcome Section */}
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Welcome to HQ, <span className="bg-gradient-to-r from-green-300 to-green-400 bg-clip-text text-transparent">{userName}</span>
            </h1>
            <p className="text-white/60 text-base">Your premium trading command center</p>
          </div>
        </div>
      </div>

      {/* Premium Bento Dashboard */}
      <div className="p-8 space-y-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Clean Progress Bar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/[0.02] backdrop-blur-sm border border-white/[0.06] rounded-xl p-6 shadow-2xl shadow-black/20"
            style={{
              boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
            }}
          >
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-white mb-1">What's Next</h3>
                <p className="text-white/50 text-sm">Complete your trading setup</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                  {Math.round((completedSteps / progressSteps.length) * 100)}%
                </div>
                <div className="text-white/40 text-xs">Complete</div>
              </div>
            </div>

            {/* Horizontal Progress Bar */}
            <div className="relative w-full h-2 bg-white/[0.05] rounded-full overflow-hidden shadow-inner">
              <motion.div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full shadow-lg shadow-green-500/20"
                initial={{ width: 0 }}
                animate={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              />
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/[0.05] to-transparent rounded-full"></div>
            </div>

            {/* Progress Steps as Small Indicators */}
            <div className="flex items-center justify-between mt-4">
              {progressSteps.map((step) => (
                <div
                  key={step.id}
                  onClick={step.action}
                  className="flex items-center gap-2 cursor-pointer group"
                >
                  <div className={`w-3 h-3 rounded-full border-2 transition-all duration-300 ${
                    step.completed
                      ? 'bg-green-400 border-green-400 shadow-lg shadow-green-400/30'
                      : 'bg-white/[0.05] border-white/[0.20] group-hover:border-white/[0.40]'
                  }`} />
                  <span className={`text-xs font-medium transition-all duration-300 ${
                    step.completed ? 'text-green-300' : 'text-white/60 group-hover:text-white/80'
                  }`}>
                    {step.title}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Premium Glassmorphism Grid Layout */}
          <div className="grid grid-cols-3 gap-6 h-[320px]">
            {/* Earnings - Horizontal Card */}
            <div
              className="col-span-1 bg-white/[0.02] backdrop-blur-sm border border-white/[0.06] rounded-xl p-5 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
              }}
            >
              {renderPremiumEarningsCard()}
            </div>

            {/* Watchlist - Square Card */}
            <div
              className="col-span-1 bg-white/[0.02] backdrop-blur-sm border border-white/[0.06] rounded-xl p-5 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
              }}
            >
              {renderPremiumWatchlistCard()}
            </div>

            {/* Your Agents - Wide Card */}
            <div
              className="col-span-1 bg-white/[0.02] backdrop-blur-sm border border-white/[0.06] rounded-xl p-5 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
              }}
            >
              {renderPremiumAgentsCard()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
