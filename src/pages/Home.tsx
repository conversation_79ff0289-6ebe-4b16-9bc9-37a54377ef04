import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  Target,
  BarChart3,
  Zap,
  Activity,
  DollarSign
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

interface UserAgent {
  id: string;
  name: string;
  description: string;
  returnPercentage: number;
  winRate: number;
  totalTrades: number;
  isActive: boolean;
  bestBacktest?: {
    returnPercentage: number;
    winRate: number;
    totalTrades: number;
    period: string;
  };
}

interface WatchlistItem {
  id: string;
  symbol: string;
  name: string;
  price: number;
  changePercent: number;
}



interface ProgressStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  completed: boolean;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress } = useGamification();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('User');

  // Enhanced mock data for premium dashboard
  const mockWatchlist: WatchlistItem[] = [
    { id: '1', symbol: 'AAPL', name: 'Apple Inc.', price: 185.42, changePercent: 2.1 },
    { id: '2', symbol: 'TSLA', name: 'Tesla Inc.', price: 248.50, changePercent: -1.8 },
    { id: '3', symbol: 'NVDA', name: 'NVIDIA Corp.', price: 875.30, changePercent: 4.2 },
    { id: '4', symbol: 'MSFT', name: 'Microsoft Corp.', price: 378.85, changePercent: 1.5 },
    { id: '5', symbol: 'GOOGL', name: 'Alphabet Inc.', price: 142.65, changePercent: -0.8 },
    { id: '6', symbol: 'META', name: 'Meta Platforms', price: 342.90, changePercent: 5.7 }
  ];

  const mockAgents: UserAgent[] = [
    {
      id: '1',
      name: 'Momentum Hunter',
      description: 'Identifies strong momentum stocks with technical analysis',
      returnPercentage: 24.5,
      winRate: 72,
      totalTrades: 45,
      isActive: true,
      bestBacktest: { returnPercentage: 24.5, winRate: 72, totalTrades: 45, period: '3M' }
    },
    {
      id: '2',
      name: 'Value Seeker',
      description: 'Finds undervalued opportunities using fundamental analysis',
      returnPercentage: 18.2,
      winRate: 68,
      totalTrades: 32,
      isActive: false,
      bestBacktest: { returnPercentage: 18.2, winRate: 68, totalTrades: 32, period: '6M' }
    },
    {
      id: '3',
      name: 'Breakout Trader',
      description: 'Captures breakout patterns with volume confirmation',
      returnPercentage: 31.8,
      winRate: 65,
      totalTrades: 28,
      isActive: true,
      bestBacktest: { returnPercentage: 31.8, winRate: 65, totalTrades: 28, period: '2M' }
    }
  ];

  const mockEarnings = {
    totalEarnings: 12847.50,
    salesCount: 23,
    monthlyGrowth: 18.4,
    topPerformer: 'Momentum Hunter'
  };

  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load agent statistics
      const { data: agents } = await supabase
        .from('agents')
        .select('id, created_at')
        .eq('user_id', user.id);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Progress steps for get started journey
  const progressSteps: ProgressStep[] = [
    {
      id: 'create-agent',
      title: 'Create Agent',
      description: 'Build your first AI trading agent',
      icon: <Zap className="w-4 h-4" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Scan',
      description: 'Discover trading opportunities',
      icon: <Target className="w-4 h-4" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Backtest Strategy',
      description: 'Test against historical data',
      icon: <BarChart3 className="w-4 h-4" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'discover-agents',
      title: 'Explore Marketplace',
      description: 'Discover community agents',
      icon: <Activity className="w-4 h-4" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    }
  ];

  const completedSteps = progressSteps.filter(step => step.completed).length;

  // Load user name
  useEffect(() => {
    const loadUserName = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.user_metadata?.full_name) {
          const firstName = user.user_metadata.full_name.split(' ')[0];
          setUserName(firstName);
        }
      } catch (error) {
        console.error('Error loading user name:', error);
      }
    };
    loadUserName();
  }, []);



  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }











  // Clean Earnings Card - Matching Reference
  const renderCleanEarningsCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center gap-3 mb-6">
        <DollarSign className="w-5 h-5 text-green-400" />
        <div>
          <h3 className="text-sm font-semibold text-white">Earnings</h3>
          <p className="text-white/50 text-xs">Total earnings</p>
        </div>
      </div>

      <div className="flex-1 flex flex-col justify-center">
        <div className="text-3xl font-bold text-green-400 mb-2">$0.00</div>
        <div className="text-white/60 text-sm mb-6">Total Earnings</div>

        <div className="grid grid-cols-2 gap-4 text-center">
          <div>
            <div className="text-green-400 text-lg font-semibold">$0.00</div>
            <div className="text-white/50 text-xs">Available</div>
          </div>
          <div>
            <div className="text-white text-lg font-semibold">0</div>
            <div className="text-white/50 text-xs">Sales</div>
          </div>
        </div>
      </div>

      <button
        onClick={() => navigate('/marketplace')}
        className="w-full mt-4 py-2 text-white/70 hover:text-white text-sm font-medium transition-all duration-200"
      >
        View Marketplace
      </button>
    </div>
  );

  // Portfolio Performance Card - Matching Reference
  const renderPortfolioCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center gap-3 mb-6">
        <BarChart3 className="w-5 h-5 text-white/70" />
        <div>
          <h3 className="text-sm font-semibold text-white">Portfolio Performance</h3>
          <p className="text-white/50 text-xs">Last 30 days</p>
        </div>
      </div>

      {/* Chart Area */}
      <div className="flex-1 relative mb-6">
        <svg width="100%" height="80" viewBox="0 0 200 80" className="overflow-visible">
          <defs>
            <linearGradient id="portfolioGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#10b981" stopOpacity="0.3"/>
              <stop offset="100%" stopColor="#10b981" stopOpacity="0"/>
            </linearGradient>
          </defs>

          {/* Chart Line */}
          <path
            d="M0,60 L20,45 L40,50 L60,35 L80,40 L100,25 L120,30 L140,20 L160,25 L180,15 L200,20"
            fill="none"
            stroke="#10b981"
            strokeWidth="2"
            className="drop-shadow-sm"
          />

          {/* Chart Fill */}
          <path
            d="M0,60 L20,45 L40,50 L60,35 L80,40 L100,25 L120,30 L140,20 L160,25 L180,15 L200,20 L200,80 L0,80 Z"
            fill="url(#portfolioGradient)"
          />
        </svg>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-3 gap-4 text-center">
        <div>
          <div className="text-green-400 text-lg font-semibold">68%</div>
          <div className="text-white/50 text-xs">Win Rate</div>
        </div>
        <div>
          <div className="text-white text-lg font-semibold">156</div>
          <div className="text-white/50 text-xs">Total Trades</div>
        </div>
        <div>
          <div className="text-white text-lg font-semibold">2:1</div>
          <div className="text-white/50 text-xs">Sharpe Ratio</div>
        </div>
      </div>
    </div>
  );

  // Clean Watchlist Card - Matching Reference
  const renderCleanWatchlistCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <TrendingUp className="w-5 h-5 text-white/70" />
          <div>
            <h3 className="text-sm font-semibold text-white">Watchlist</h3>
            <p className="text-white/50 text-xs">Saved stocks</p>
          </div>
        </div>
        <button
          onClick={() => navigate('/stock-screener')}
          className="text-white/60 hover:text-white text-xs font-medium transition-all duration-200"
        >
          Add Stocks
        </button>
      </div>

      {/* Stock List */}
      <div className="flex-1 space-y-4">
        {mockWatchlist.slice(0, 3).map((item) => (
          <div key={item.id} className="flex items-center justify-between group cursor-pointer">
            <div className="flex items-center gap-4">
              <div className="font-semibold text-white">{item.symbol}</div>

              {/* Mini Chart */}
              <div className="w-16 h-8">
                <svg width="100%" height="100%" viewBox="0 0 64 32" preserveAspectRatio="none">
                  <path
                    d={`M0,16 ${Array.from({length: 12}, (_, i) => {
                      const x = (i / 11) * 64;
                      const baseY = 16;
                      const trend = item.changePercent >= 0 ? -1 : 1;
                      const noise = (Math.random() - 0.5) * 6;
                      const trendEffect = (i / 11) * trend * 8;
                      const y = baseY + trendEffect + noise;
                      return `L${x},${Math.max(2, Math.min(30, y))}`;
                    }).join(' ')}`}
                    fill="none"
                    stroke={item.changePercent >= 0 ? '#10b981' : '#ef4444'}
                    strokeWidth="1.5"
                    opacity="0.8"
                  />
                </svg>
              </div>
            </div>

            <div className="text-right">
              <div className={`text-sm font-semibold ${
                item.changePercent >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                {item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(2)}%
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Clean Agents Card - Matching Reference
  const renderCleanAgentsCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Activity className="w-5 h-5 text-white/70" />
          <div>
            <h3 className="text-sm font-semibold text-white">Your Agents</h3>
            <p className="text-white/50 text-xs">AI trading strategies</p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-green-400 text-lg font-bold">+12.4%</div>
          <div className="text-white/50 text-xs">Total Return</div>
        </div>
      </div>

      {/* Single Agent Summary */}
      <div className="flex-1 flex flex-col justify-center text-center">
        <div className="mb-8">
          <div className="w-12 h-12 bg-white/[0.05] rounded-full flex items-center justify-center mx-auto mb-4 border border-white/[0.10]">
            <div className="w-3 h-3 bg-green-400 rounded-full"></div>
          </div>
          <h4 className="text-white font-medium mb-1">Momentum Trader</h4>
          <p className="text-white/50 text-sm">Active strategy</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-3 gap-6 text-center">
          <div>
            <div className="text-green-400 text-xl font-bold">68%</div>
            <div className="text-white/50 text-xs">Win Rate</div>
          </div>
          <div>
            <div className="text-white text-xl font-bold">156</div>
            <div className="text-white/50 text-xs">Total Trades</div>
          </div>
          <div>
            <div className="text-white text-xl font-bold">2:1</div>
            <div className="text-white/50 text-xs">Sharpe Ratio</div>
          </div>
        </div>
      </div>

      {/* Action Button */}
      <button
        onClick={() => navigate('/builder')}
        className="w-full mt-6 py-2 text-white/70 hover:text-white text-sm font-medium transition-all duration-200"
      >
        Manage Agents
      </button>
    </div>
  );



  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      {/* Clean Header */}
      <div className="px-8 py-8">
        <div>
          <h1 className="text-4xl font-bold text-white mb-2">
            Welcome to HQ, <span className="bg-gradient-to-r from-green-300 to-green-400 bg-clip-text text-transparent">{userName}</span>
          </h1>
          <p className="text-white/50 text-lg">Your premium trading command center</p>
        </div>
      </div>

      {/* Premium Bento Dashboard */}
      <div className="p-8 space-y-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* What's Next - No Box */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-semibold text-white mb-1">What's Next</h3>
                <p className="text-white/40 text-sm">Complete your trading setup</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                  {Math.round((completedSteps / progressSteps.length) * 100)}%
                </div>
                <div className="text-white/30 text-xs">Complete</div>
              </div>
            </div>

            {/* Clean Horizontal Progress Bar */}
            <div className="relative w-full h-1.5 bg-white/[0.08] rounded-full overflow-hidden mb-6">
              <motion.div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              />
            </div>

            {/* Progress Steps as Clean Indicators */}
            <div className="flex items-center justify-between">
              {progressSteps.map((step) => (
                <div
                  key={step.id}
                  onClick={step.action}
                  className="flex items-center gap-2 cursor-pointer group"
                >
                  <div className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                    step.completed
                      ? 'bg-green-400'
                      : 'bg-white/[0.15] group-hover:bg-white/[0.30]'
                  }`} />
                  <span className={`text-xs font-medium transition-all duration-300 ${
                    step.completed ? 'text-green-300' : 'text-white/50 group-hover:text-white/70'
                  }`}>
                    {step.title}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Clean Bento Layout - Matching Reference */}
          <div className="grid grid-cols-3 gap-6 h-[420px]">
            {/* Left Column - Earnings & Portfolio */}
            <div className="space-y-6">
              {/* Earnings Card */}
              <div
                className="relative bg-white/[0.02] backdrop-blur-sm border border-white/[0.05] rounded-xl p-6 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
                style={{
                  boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
                }}
              >
                {renderCleanEarningsCard()}
              </div>

              {/* Portfolio Performance Card */}
              <div
                className="relative bg-white/[0.02] backdrop-blur-sm border border-white/[0.05] rounded-xl p-6 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
                style={{
                  boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
                }}
              >
                {renderPortfolioCard()}
              </div>
            </div>

            {/* Middle Column - Watchlist */}
            <div
              className="relative bg-white/[0.02] backdrop-blur-sm border border-white/[0.05] rounded-xl p-6 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
              }}
            >
              {renderCleanWatchlistCard()}
            </div>

            {/* Right Column - Your Agents */}
            <div
              className="relative bg-white/[0.02] backdrop-blur-sm border border-white/[0.05] rounded-xl p-6 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
              }}
            >
              {renderCleanAgentsCard()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
