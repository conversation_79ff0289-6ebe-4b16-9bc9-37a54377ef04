import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  Target,
  BarChart3,
  Zap,
  Activity,
  DollarSign
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

interface UserAgent {
  id: string;
  name: string;
  description: string;
  returnPercentage: number;
  winRate: number;
  totalTrades: number;
  isActive: boolean;
  bestBacktest?: {
    returnPercentage: number;
    winRate: number;
    totalTrades: number;
    period: string;
  };
}





interface ProgressStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  completed: boolean;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress } = useGamification();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('User');
  const [watchlistStocks, setWatchlistStocks] = useState<any[]>([]);



  const mockAgents: UserAgent[] = [
    {
      id: '1',
      name: 'Momentum Hunter',
      description: 'Identifies strong momentum stocks with technical analysis',
      returnPercentage: 24.5,
      winRate: 72,
      totalTrades: 45,
      isActive: true,
      bestBacktest: { returnPercentage: 24.5, winRate: 72, totalTrades: 45, period: '3M' }
    },
    {
      id: '2',
      name: 'Value Seeker',
      description: 'Finds undervalued opportunities using fundamental analysis',
      returnPercentage: 18.2,
      winRate: 68,
      totalTrades: 32,
      isActive: false,
      bestBacktest: { returnPercentage: 18.2, winRate: 68, totalTrades: 32, period: '6M' }
    },
    {
      id: '3',
      name: 'Breakout Trader',
      description: 'Captures breakout patterns with volume confirmation',
      returnPercentage: 31.8,
      winRate: 65,
      totalTrades: 28,
      isActive: true,
      bestBacktest: { returnPercentage: 31.8, winRate: 65, totalTrades: 28, period: '2M' }
    }
  ];



  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load agent statistics
      const { data: agents } = await supabase
        .from('agents')
        .select('id, created_at')
        .eq('user_id', user.id);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Progress steps for get started journey
  const progressSteps: ProgressStep[] = [
    {
      id: 'create-agent',
      title: 'Create Agent',
      description: 'Build your first AI trading agent',
      icon: <Zap className="w-4 h-4" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Scan',
      description: 'Discover trading opportunities',
      icon: <Target className="w-4 h-4" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Backtest Strategy',
      description: 'Test against historical data',
      icon: <BarChart3 className="w-4 h-4" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'discover-agents',
      title: 'Explore Marketplace',
      description: 'Discover community agents',
      icon: <Activity className="w-4 h-4" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    }
  ];

  const completedSteps = progressSteps.filter(step => step.completed).length;

  // Load user name
  useEffect(() => {
    const loadUserName = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.user_metadata?.full_name) {
          const firstName = user.user_metadata.full_name.split(' ')[0];
          setUserName(firstName);
        }
      } catch (error) {
        console.error('Error loading user name:', error);
      }
    };
    loadUserName();
  }, []);

  // Load watchlist
  useEffect(() => {
    fetchWatchlist();
  }, []);



  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }











  // Standout Earnings Card - White Background
  const renderStandoutEarningsCard = () => (
    <div className="h-full flex flex-col">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 bg-green-500/10 rounded-lg flex items-center justify-center border border-green-500/20">
          <DollarSign className="w-4 h-4 text-green-600" />
        </div>
        <div>
          <h3 className="text-sm font-bold text-gray-900">Earnings</h3>
          <p className="text-gray-500 text-xs">Total revenue</p>
        </div>
      </div>

      <div className="flex-1 flex flex-col justify-center text-center">
        <div className="text-2xl font-bold text-gray-900 mb-1">$0.00</div>
        <div className="text-gray-500 text-xs mb-4">Total Earnings</div>

        <div className="grid grid-cols-2 gap-3 text-center">
          <div className="bg-gray-50 rounded-lg p-2 border border-gray-100">
            <div className="text-gray-900 text-sm font-semibold">$0.00</div>
            <div className="text-gray-500 text-xs">Available</div>
          </div>
          <div className="bg-gray-50 rounded-lg p-2 border border-gray-100">
            <div className="text-gray-900 text-sm font-semibold">0</div>
            <div className="text-gray-500 text-xs">Sales</div>
          </div>
        </div>
      </div>

      <button
        onClick={() => navigate('/marketplace')}
        className="w-full mt-3 py-2 bg-gray-900 text-white text-xs font-medium rounded-lg hover:bg-gray-800 transition-all duration-200"
      >
        View Marketplace
      </button>
    </div>
  );



  const fetchWatchlist = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('user_watchlist')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setWatchlistStocks(data || []);
    } catch (error) {
      console.error('Error fetching watchlist:', error);
    }
  };

  const renderFunctionalWatchlistCard = () => (
    <div className="h-full flex flex-col">
      <div className="mb-4">
        <h3 className="text-lg font-bold text-white mb-1">Watchlist</h3>
        <p className="text-white/50 text-xs">Saved stocks</p>
      </div>

      {/* Horizontal Stock List */}
      <div className="flex-1">
        {watchlistStocks.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <TrendingUp className="w-8 h-8 text-white/30 mx-auto mb-2" />
              <p className="text-white/50 text-sm mb-3">No stocks saved</p>
              <button
                onClick={() => navigate('/stock-screener')}
                className="text-white/70 hover:text-white text-xs font-medium transition-all duration-200"
              >
                Browse Stocks
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {watchlistStocks.slice(0, 5).map((stock) => (
              <div
                key={stock.id}
                className="flex items-center justify-between py-2 px-3 bg-white/[0.02] rounded-lg border border-white/[0.05] hover:bg-white/[0.04] transition-all duration-200 cursor-pointer"
                onClick={() => navigate('/stock-screener')}
              >
                <div className="flex items-center gap-3">
                  <div className="font-semibold text-white text-sm">{stock.symbol}</div>
                  <div className="w-12 h-6">
                    <svg width="100%" height="100%" viewBox="0 0 48 24" preserveAspectRatio="none">
                      <path
                        d={`M0,12 ${Array.from({length: 8}, (_, i) => {
                          const x = (i / 7) * 48;
                          const baseY = 12;
                          const trend = Math.random() > 0.5 ? -1 : 1;
                          const noise = (Math.random() - 0.5) * 4;
                          const trendEffect = (i / 7) * trend * 6;
                          const y = baseY + trendEffect + noise;
                          return `L${x},${Math.max(2, Math.min(22, y))}`;
                        }).join(' ')}`}
                        fill="none"
                        stroke="#6b7280"
                        strokeWidth="1"
                        opacity="0.6"
                      />
                    </svg>
                  </div>
                </div>
                <div className="text-white/60 text-xs">
                  {stock.price ? `$${stock.price}` : '--'}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <button
        onClick={() => navigate('/stock-screener')}
        className="w-full mt-3 py-2 text-white/70 hover:text-white text-xs font-medium transition-all duration-200"
      >
        Open Scanner
      </button>
    </div>
  );

  // Clean Row-Based Agents Card
  const renderCleanRowAgentsCard = () => {
    // Sort agents by win rate (highest first)
    const sortedAgents = [...mockAgents].sort((a, b) => b.winRate - a.winRate);

    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-bold text-white mb-1">Your Agents</h3>
            <p className="text-white/50 text-xs">AI trading strategies</p>
          </div>
          <div className="text-right">
            <div className="text-green-400 text-lg font-bold">+12.4%</div>
            <div className="text-white/50 text-xs">Total Return</div>
          </div>
        </div>

        {/* Agents List - Clean Rows */}
        <div className="flex-1 space-y-2">
          {sortedAgents.map((agent, index) => (
            <div
              key={agent.id}
              className="flex items-center justify-between py-3 px-4 bg-white/[0.02] rounded-lg border border-white/[0.05] hover:bg-white/[0.04] hover:border-white/[0.08] transition-all duration-200 cursor-pointer"
              onClick={() => navigate('/builder')}
            >
              {/* Left Side - Rank & Name */}
              <div className="flex items-center gap-4">
                <div className="text-white font-bold text-lg min-w-[24px]">#{index + 1}</div>
                <div>
                  <div className="font-semibold text-white text-sm">{agent.name}</div>
                  <div className="text-white/50 text-xs">{agent.description}</div>
                </div>
              </div>

              {/* Right Side - Stats */}
              <div className="flex items-center gap-6 text-right">
                <div>
                  <div className="text-white font-semibold text-sm">{agent.winRate}%</div>
                  <div className="text-white/40 text-xs">Win Rate</div>
                </div>
                <div>
                  <div className={`text-sm font-bold ${
                    agent.returnPercentage >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {agent.returnPercentage >= 0 ? '+' : ''}{agent.returnPercentage}%
                  </div>
                  <div className="text-white/40 text-xs">Return</div>
                </div>
                <div>
                  <div className="text-white font-semibold text-sm">{agent.totalTrades}</div>
                  <div className="text-white/40 text-xs">Trades</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {mockAgents.length === 0 && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Activity className="w-8 h-8 text-white/30 mx-auto mb-3" />
              <h4 className="text-white/60 text-base font-medium mb-2">No agents created yet</h4>
              <p className="text-white/40 text-sm mb-4">Create your first AI trading agent</p>
              <button
                onClick={() => navigate('/builder')}
                className="bg-white/[0.05] border border-white/[0.10] text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-white/[0.08] hover:border-white/[0.15] transition-all duration-200"
              >
                Create Agent
              </button>
            </div>
          </div>
        )}

        {/* Action Button */}
        <button
          onClick={() => navigate('/builder')}
          className="w-full mt-4 py-2 text-white/70 hover:text-white text-sm font-medium transition-all duration-200"
        >
          Manage All Agents
        </button>
      </div>
    );
  };



  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      {/* Clean Header */}
      <div className="px-8 py-8">
        <div>
          <h1 className="text-4xl font-bold text-white mb-2">
            Welcome to HQ, <span className="bg-gradient-to-r from-green-300 to-green-400 bg-clip-text text-transparent">{userName}</span>
          </h1>
          <p className="text-white/50 text-lg">Your premium trading command center</p>
        </div>
      </div>

      {/* Premium Bento Dashboard */}
      <div className="p-8 space-y-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* What's Next - No Box */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-semibold text-white mb-1">What's Next</h3>
                <p className="text-white/40 text-sm">Complete your trading setup</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                  {Math.round((completedSteps / progressSteps.length) * 100)}%
                </div>
                <div className="text-white/30 text-xs">Complete</div>
              </div>
            </div>

            {/* Clean Horizontal Progress Bar */}
            <div className="relative w-full h-1.5 bg-white/[0.08] rounded-full overflow-hidden mb-6">
              <motion.div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              />
            </div>

            {/* Progress Steps as Clean Indicators */}
            <div className="flex items-center justify-between">
              {progressSteps.map((step) => (
                <div
                  key={step.id}
                  onClick={step.action}
                  className="flex items-center gap-2 cursor-pointer group"
                >
                  <div className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                    step.completed
                      ? 'bg-green-400'
                      : 'bg-white/[0.15] group-hover:bg-white/[0.30]'
                  }`} />
                  <span className={`text-xs font-medium transition-all duration-300 ${
                    step.completed ? 'text-green-300' : 'text-white/50 group-hover:text-white/70'
                  }`}>
                    {step.title}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Clean Bento Layout - Proper Grid */}
          <div className="grid grid-cols-4 grid-rows-2 gap-6 h-[400px]">
            {/* Earnings Card - Standout White Card */}
            <div
              className="col-span-1 row-span-1 relative bg-white/[0.95] backdrop-blur-sm border border-white/[0.20] rounded-xl p-6 transition-all duration-300 hover:bg-white shadow-lg"
              style={{
                boxShadow: '0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.1)'
              }}
            >
              {renderStandoutEarningsCard()}
            </div>

            {/* Watchlist Card - Square */}
            <div
              className="col-span-1 row-span-1 relative bg-white/[0.02] backdrop-blur-sm border border-white/[0.05] rounded-xl p-6 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
              }}
            >
              {renderFunctionalWatchlistCard()}
            </div>

            {/* Your Agents Card - Wide Rectangle */}
            <div
              className="col-span-2 row-span-2 relative bg-white/[0.02] backdrop-blur-sm border border-white/[0.05] rounded-xl p-6 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
              }}
            >
              {renderCleanRowAgentsCard()}
            </div>

            {/* Additional Square Card */}
            <div
              className="col-span-1 row-span-1 relative bg-white/[0.02] backdrop-blur-sm border border-white/[0.05] rounded-xl p-6 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
              }}
            >
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-10 h-10 bg-white/[0.08] backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 border border-white/[0.15] shadow-inner">
                    <Target className="w-5 h-5 text-white/60" />
                  </div>
                  <div className="text-white/50 text-sm font-medium">Analytics</div>
                  <div className="text-white/30 text-xs mt-1">Coming soon</div>
                </div>
              </div>
            </div>

            {/* Another Square Card */}
            <div
              className="col-span-1 row-span-1 relative bg-white/[0.02] backdrop-blur-sm border border-white/[0.05] rounded-xl p-6 transition-all duration-300 hover:bg-white/[0.03] hover:border-white/[0.08]"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.3), 0 4px 6px rgba(0,0,0,0.1)'
              }}
            >
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-10 h-10 bg-white/[0.08] backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 border border-white/[0.15] shadow-inner">
                    <Zap className="w-5 h-5 text-white/60" />
                  </div>
                  <div className="text-white/50 text-sm font-medium">Tools</div>
                  <div className="text-white/30 text-xs mt-1">More features</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
