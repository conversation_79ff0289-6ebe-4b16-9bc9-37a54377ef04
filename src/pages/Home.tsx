import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  Target,
  BarChart3,
  Zap,
  Activity,
  DollarSign,
  Plus
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

interface UserAgent {
  id: string;
  name: string;
  description: string;
  returnPercentage: number;
  winRate: number;
  totalTrades: number;
  isActive: boolean;
  bestBacktest?: {
    returnPercentage: number;
    winRate: number;
    totalTrades: number;
    period: string;
  };
}

interface WatchlistItem {
  id: string;
  symbol: string;
  name: string;
  price: number;
  changePercent: number;
}



interface ProgressStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  completed: boolean;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress } = useGamification();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('User');

  // Enhanced mock data for premium dashboard
  const mockWatchlist: WatchlistItem[] = [
    { id: '1', symbol: 'AAPL', name: 'Apple Inc.', price: 185.42, changePercent: 2.1 },
    { id: '2', symbol: 'TSLA', name: 'Tesla Inc.', price: 248.50, changePercent: -1.8 },
    { id: '3', symbol: 'NVDA', name: 'NVIDIA Corp.', price: 875.30, changePercent: 4.2 },
    { id: '4', symbol: 'MSFT', name: 'Microsoft Corp.', price: 378.85, changePercent: 1.5 },
    { id: '5', symbol: 'GOOGL', name: 'Alphabet Inc.', price: 142.65, changePercent: -0.8 },
    { id: '6', symbol: 'META', name: 'Meta Platforms', price: 342.90, changePercent: 5.7 }
  ];

  const mockAgents: UserAgent[] = [
    {
      id: '1',
      name: 'Momentum Hunter',
      description: 'Identifies strong momentum stocks with technical analysis',
      returnPercentage: 24.5,
      winRate: 72,
      totalTrades: 45,
      isActive: true,
      bestBacktest: { returnPercentage: 24.5, winRate: 72, totalTrades: 45, period: '3M' }
    },
    {
      id: '2',
      name: 'Value Seeker',
      description: 'Finds undervalued opportunities using fundamental analysis',
      returnPercentage: 18.2,
      winRate: 68,
      totalTrades: 32,
      isActive: false,
      bestBacktest: { returnPercentage: 18.2, winRate: 68, totalTrades: 32, period: '6M' }
    },
    {
      id: '3',
      name: 'Breakout Trader',
      description: 'Captures breakout patterns with volume confirmation',
      returnPercentage: 31.8,
      winRate: 65,
      totalTrades: 28,
      isActive: true,
      bestBacktest: { returnPercentage: 31.8, winRate: 65, totalTrades: 28, period: '2M' }
    }
  ];

  const mockEarnings = {
    totalEarnings: 12847.50,
    salesCount: 23,
    monthlyGrowth: 18.4,
    topPerformer: 'Momentum Hunter'
  };

  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load agent statistics
      const { data: agents } = await supabase
        .from('agents')
        .select('id, created_at')
        .eq('user_id', user.id);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Progress steps for get started journey
  const progressSteps: ProgressStep[] = [
    {
      id: 'create-agent',
      title: 'Create Agent',
      description: 'Build your first AI trading agent',
      icon: <Zap className="w-4 h-4" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Scan',
      description: 'Discover trading opportunities',
      icon: <Target className="w-4 h-4" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Backtest Strategy',
      description: 'Test against historical data',
      icon: <BarChart3 className="w-4 h-4" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'discover-agents',
      title: 'Explore Marketplace',
      description: 'Discover community agents',
      icon: <Activity className="w-4 h-4" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    }
  ];

  const completedSteps = progressSteps.filter(step => step.completed).length;

  // Load user name
  useEffect(() => {
    const loadUserName = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.user_metadata?.full_name) {
          const firstName = user.user_metadata.full_name.split(' ')[0];
          setUserName(firstName);
        }
      } catch (error) {
        console.error('Error loading user name:', error);
      }
    };
    loadUserName();
  }, []);



  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }











  // Premium Earnings Card for Bento Layout
  const renderPremiumEarningsCard = () => (
    <div className="relative h-full flex flex-col justify-between p-1">
      {/* Header */}
      <div className="text-center mb-2">
        <div className="w-8 h-8 bg-gradient-to-br from-green-500/15 to-green-400/5 rounded-lg flex items-center justify-center mx-auto border border-green-500/20 backdrop-blur-sm shadow-inner mb-2">
          <DollarSign className="w-4 h-4 text-green-400" />
        </div>
        <h3 className="text-xs font-semibold text-white/90">Total Earnings</h3>
      </div>

      {/* Main Amount */}
      <div className="text-center mb-3">
        <div className="text-xl font-bold text-green-300 mb-1">${mockEarnings.totalEarnings.toLocaleString()}</div>
        <div className="text-green-400 text-xs">+{mockEarnings.monthlyGrowth}% this month</div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-1.5 mb-3">
        <div className="bg-white/[0.03] backdrop-blur-sm rounded-md p-1.5 border border-white/[0.08] shadow-inner text-center">
          <div className="text-xs font-semibold text-white">{mockEarnings.salesCount}</div>
          <div className="text-white/40 text-[10px]">Sales</div>
        </div>
        <div className="bg-white/[0.03] backdrop-blur-sm rounded-md p-1.5 border border-white/[0.08] shadow-inner text-center">
          <div className="text-xs font-semibold text-white">$2.1K</div>
          <div className="text-white/40 text-[10px]">Avg Sale</div>
        </div>
      </div>

      {/* Action Button */}
      <button
        onClick={() => navigate('/marketplace')}
        className="w-full px-2 py-1.5 bg-white/[0.05] backdrop-blur-sm border border-white/[0.12] rounded-md text-white/80 hover:text-white hover:bg-white/[0.08] hover:border-white/[0.20] text-[10px] font-medium transition-all duration-200 shadow-inner"
      >
        View Details
      </button>
    </div>
  );

  // Premium Watchlist Card for Bento Layout
  const renderPremiumWatchlistCard = () => (
    <div className="h-full flex flex-col p-1">
      {/* Header */}
      <div className="text-center mb-2">
        <div className="w-8 h-8 bg-gradient-to-br from-blue-500/15 to-blue-400/5 backdrop-blur-sm rounded-lg flex items-center justify-center mx-auto border border-blue-500/20 shadow-inner mb-2">
          <TrendingUp className="w-4 h-4 text-blue-400" />
        </div>
        <h3 className="text-xs font-semibold text-white/90">Watchlist</h3>
      </div>

      {/* Compact Stock List */}
      <div className="flex-1 space-y-1.5 mb-2">
        {mockWatchlist.slice(0, 4).map((item) => (
          <div key={item.id} className="relative bg-white/[0.04] backdrop-blur-md rounded-md border border-white/[0.08] hover:border-white/[0.12] hover:bg-white/[0.06] transition-all duration-300 cursor-pointer overflow-hidden shadow-inner">
            <div className="p-2">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="font-semibold text-white text-xs">{item.symbol}</div>
                  <div className="text-white/40 text-[10px]">${item.price.toFixed(2)}</div>
                </div>

                {/* Mini Sparkline */}
                <div className="w-8 h-4 mx-2">
                  <svg width="100%" height="100%" viewBox="0 0 32 16" preserveAspectRatio="none" className="opacity-60">
                    <path
                      d={`M0,8 ${Array.from({length: 8}, (_, i) => {
                        const x = (i / 7) * 32;
                        const baseY = 8;
                        const trend = item.changePercent >= 0 ? -1 : 1;
                        const noise = (Math.random() - 0.5) * 3;
                        const trendEffect = (i / 7) * trend * 3;
                        const y = baseY + trendEffect + noise;
                        return `L${x},${Math.max(1, Math.min(15, y))}`;
                      }).join(' ')}`}
                      fill="none"
                      stroke={item.changePercent >= 0 ? '#6b7280' : '#6b7280'}
                      strokeWidth="1"
                      opacity="0.8"
                    />
                  </svg>
                </div>

                <div className="text-right">
                  <div className={`text-xs font-medium ${
                    item.changePercent >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(1)}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Action Button */}
      <button
        onClick={() => navigate('/stock-screener')}
        className="w-full bg-white/[0.05] backdrop-blur-sm border border-white/[0.12] text-white/80 font-medium py-1.5 px-2 rounded-md hover:border-white/[0.20] hover:bg-white/[0.08] transition-all duration-300 text-[10px] shadow-inner"
      >
        Open Scanner
      </button>
    </div>
  );

  // Premium Agents Card for Bento Layout
  const renderPremiumAgentsCard = () => (
    <div className="h-full flex flex-col relative">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-500/15 to-purple-400/5 backdrop-blur-sm rounded-xl flex items-center justify-center border border-purple-500/20 shadow-inner">
            <Activity className="w-6 h-6 text-purple-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Your Agents</h3>
            <p className="text-white/50 text-sm">AI trading strategies</p>
          </div>
        </div>
        <button
          onClick={() => navigate('/builder')}
          className="bg-white/[0.08] backdrop-blur-sm border border-white/[0.12] text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-white/[0.12] hover:border-white/[0.20] transition-all duration-200 shadow-inner"
        >
          <Plus className="w-4 h-4 inline mr-2" />
          Create New
        </button>
      </div>

      {/* Agents List */}
      <div className="flex-1 space-y-4">
        {mockAgents.map((agent) => (
          <div
            key={agent.id}
            className="p-5 bg-white/[0.04] backdrop-blur-md rounded-xl border border-white/[0.08] hover:border-white/[0.12] hover:bg-white/[0.06] cursor-pointer transition-all duration-300 shadow-inner"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <div className={`w-10 h-10 rounded-xl flex items-center justify-center border backdrop-blur-sm ${
                  agent.isActive
                    ? 'bg-white/[0.10] border-white/[0.20] shadow-inner'
                    : 'bg-white/[0.06] border-white/[0.12]'
                }`}>
                  <div className={`w-4 h-4 rounded-full ${
                    agent.isActive ? 'bg-green-400 shadow-lg shadow-green-400/30' : 'bg-white/40'
                  }`} />
                </div>
                <div>
                  <div className="font-semibold text-white text-base">{agent.name}</div>
                  <div className="text-white/50 text-sm">{agent.description}</div>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-lg font-bold ${
                  agent.returnPercentage >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {agent.returnPercentage >= 0 ? '+' : ''}{agent.returnPercentage}%
                </div>
                <div className="text-white/40 text-xs">Total Return</div>
              </div>
            </div>

            <div className="grid grid-cols-4 gap-3">
              <div className="bg-white/[0.04] backdrop-blur-sm rounded-lg p-3 border border-white/[0.08] shadow-inner text-center">
                <div className="text-sm font-semibold text-white">{agent.winRate}%</div>
                <div className="text-white/40 text-xs">Win Rate</div>
              </div>
              <div className="bg-white/[0.04] backdrop-blur-sm rounded-lg p-3 border border-white/[0.08] shadow-inner text-center">
                <div className="text-sm font-semibold text-white">{agent.totalTrades}</div>
                <div className="text-white/40 text-xs">Trades</div>
              </div>
              <div className="bg-white/[0.04] backdrop-blur-sm rounded-lg p-3 border border-white/[0.08] shadow-inner text-center">
                <div className="text-sm font-semibold text-white">{agent.bestBacktest?.period || '3M'}</div>
                <div className="text-white/40 text-xs">Period</div>
              </div>
              <div className="bg-white/[0.04] backdrop-blur-sm rounded-lg p-3 border border-white/[0.08] shadow-inner text-center">
                <div className={`text-sm font-semibold ${agent.isActive ? 'text-green-400' : 'text-white/60'}`}>
                  {agent.isActive ? 'Active' : 'Paused'}
                </div>
                <div className="text-white/40 text-xs">Status</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {mockAgents.length === 0 && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-white/[0.08] backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-4 border border-white/[0.15]">
              <Activity className="w-8 h-8 text-white/40" />
            </div>
            <h4 className="text-white/60 text-lg font-medium mb-2">No agents created yet</h4>
            <p className="text-white/40 text-sm mb-6">Create your first AI trading agent to get started</p>
            <button
              onClick={() => navigate('/builder')}
              className="bg-white/[0.08] backdrop-blur-sm border border-white/[0.12] text-white text-sm font-medium py-3 px-6 rounded-lg hover:bg-white/[0.12] hover:border-white/[0.20] transition-all duration-200 shadow-inner"
            >
              <Plus className="w-4 h-4 inline mr-2" />
              Create Your First Agent
            </button>
          </div>
        </div>
      )}
    </div>
  );



  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      {/* Clean Header */}
      <div className="px-8 py-8">
        <div>
          <h1 className="text-4xl font-bold text-white mb-2">
            Welcome to HQ, <span className="bg-gradient-to-r from-green-300 to-green-400 bg-clip-text text-transparent">{userName}</span>
          </h1>
          <p className="text-white/50 text-lg">Your premium trading command center</p>
        </div>
      </div>

      {/* Premium Bento Dashboard */}
      <div className="p-8 space-y-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* What's Next - No Box */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-semibold text-white mb-1">What's Next</h3>
                <p className="text-white/40 text-sm">Complete your trading setup</p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-green-300 bg-clip-text text-transparent">
                  {Math.round((completedSteps / progressSteps.length) * 100)}%
                </div>
                <div className="text-white/30 text-xs">Complete</div>
              </div>
            </div>

            {/* Clean Horizontal Progress Bar */}
            <div className="relative w-full h-1.5 bg-white/[0.08] rounded-full overflow-hidden mb-6">
              <motion.div
                className="absolute top-0 left-0 h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              />
            </div>

            {/* Progress Steps as Clean Indicators */}
            <div className="flex items-center justify-between">
              {progressSteps.map((step) => (
                <div
                  key={step.id}
                  onClick={step.action}
                  className="flex items-center gap-2 cursor-pointer group"
                >
                  <div className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                    step.completed
                      ? 'bg-green-400'
                      : 'bg-white/[0.15] group-hover:bg-white/[0.30]'
                  }`} />
                  <span className={`text-xs font-medium transition-all duration-300 ${
                    step.completed ? 'text-green-300' : 'text-white/50 group-hover:text-white/70'
                  }`}>
                    {step.title}
                  </span>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Premium Bento Box Layout */}
          <div className="grid grid-cols-4 grid-rows-2 gap-6 h-[400px]">
            {/* Earnings - Square Card (Top Left) */}
            <div
              className="col-span-1 row-span-1 relative bg-white/[0.03] backdrop-blur-md border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-white/[0.05] hover:border-white/[0.12] group"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.15), inset 0 -1px 0 rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.3)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent rounded-2xl"></div>
              <div className="relative z-10">
                {renderPremiumEarningsCard()}
              </div>
            </div>

            {/* Watchlist - Square Card (Top Right) */}
            <div
              className="col-span-1 row-span-1 relative bg-white/[0.03] backdrop-blur-md border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-white/[0.05] hover:border-white/[0.12] group"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.15), inset 0 -1px 0 rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.3)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent rounded-2xl"></div>
              <div className="relative z-10">
                {renderPremiumWatchlistCard()}
              </div>
            </div>

            {/* Your Agents - Wide Card (Spans 2 columns, 2 rows) */}
            <div
              className="col-span-2 row-span-2 relative bg-white/[0.03] backdrop-blur-md border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-white/[0.05] hover:border-white/[0.12] group"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.15), inset 0 -1px 0 rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.3)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent rounded-2xl"></div>
              <div className="relative z-10">
                {renderPremiumAgentsCard()}
              </div>
            </div>

            {/* Additional Card - Square (Bottom Left) */}
            <div
              className="col-span-1 row-span-1 relative bg-white/[0.03] backdrop-blur-md border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-white/[0.05] hover:border-white/[0.12] group"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.15), inset 0 -1px 0 rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.3)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent rounded-2xl"></div>
              <div className="relative z-10 h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-10 h-10 bg-white/[0.08] backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 border border-white/[0.15] shadow-inner">
                    <Zap className="w-5 h-5 text-white/60" />
                  </div>
                  <div className="text-white/50 text-sm font-medium">Coming Soon</div>
                  <div className="text-white/30 text-xs mt-1">More features</div>
                </div>
              </div>
            </div>

            {/* Another Card - Square (Bottom Right) */}
            <div
              className="col-span-1 row-span-1 relative bg-white/[0.03] backdrop-blur-md border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-white/[0.05] hover:border-white/[0.12] group"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.15), inset 0 -1px 0 rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.3)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] to-transparent rounded-2xl"></div>
              <div className="relative z-10 h-full flex items-center justify-center">
                <div className="text-center">
                  <div className="w-10 h-10 bg-white/[0.08] backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 border border-white/[0.15] shadow-inner">
                    <Target className="w-5 h-5 text-white/60" />
                  </div>
                  <div className="text-white/50 text-sm font-medium">Analytics</div>
                  <div className="text-white/30 text-xs mt-1">Performance insights</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
