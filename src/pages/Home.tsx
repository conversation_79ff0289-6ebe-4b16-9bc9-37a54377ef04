import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Target,
  BarChart3,
  Zap,
  Activity,
  DollarSign
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useGamification } from '@/contexts/GamificationContext';

interface UserStats {
  agentsCreated: number;
  scansCompleted: number;
  backtestsCompleted: number;
  portfoliosCreated: number;
  winRate: number;
  totalTrades: number;
  successfulTrades: number;
  timeSavedHours: number;
  lastActivityDate: string | null;
}

interface UserAgent {
  id: string;
  name: string;
  description: string;
  returnPercentage: number;
  winRate: number;
  totalTrades: number;
  isActive: boolean;
  bestBacktest?: {
    returnPercentage: number;
    winRate: number;
    totalTrades: number;
    period: string;
  };
}





interface ProgressStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  completed: boolean;
}

const Home: React.FC = () => {
  const navigate = useNavigate();
  const { userProgress } = useGamification();

  // State for real user data
  const [userStats, setUserStats] = useState<UserStats>({
    agentsCreated: 0,
    scansCompleted: 0,
    backtestsCompleted: 0,
    portfoliosCreated: 0,
    winRate: 0,
    totalTrades: 0,
    successfulTrades: 0,
    timeSavedHours: 0,
    lastActivityDate: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('User');
  const [watchlistStocks, setWatchlistStocks] = useState<any[]>([]);

  const fetchWatchlist = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data, error } = await supabase
        .from('user_watchlist')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setWatchlistStocks(data || []);
    } catch (error) {
      console.error('Error fetching watchlist:', error);
    }
  };



  const mockAgents: UserAgent[] = [
    {
      id: '1',
      name: 'Momentum Hunter',
      description: 'Identifies strong momentum stocks with technical analysis',
      returnPercentage: 24.5,
      winRate: 72,
      totalTrades: 45,
      isActive: true,
      bestBacktest: { returnPercentage: 24.5, winRate: 72, totalTrades: 45, period: '3M' }
    },
    {
      id: '2',
      name: 'Value Seeker',
      description: 'Finds undervalued opportunities using fundamental analysis',
      returnPercentage: 18.2,
      winRate: 68,
      totalTrades: 32,
      isActive: false,
      bestBacktest: { returnPercentage: 18.2, winRate: 68, totalTrades: 32, period: '6M' }
    },
    {
      id: '3',
      name: 'Breakout Trader',
      description: 'Captures breakout patterns with volume confirmation',
      returnPercentage: 31.8,
      winRate: 65,
      totalTrades: 28,
      isActive: true,
      bestBacktest: { returnPercentage: 31.8, winRate: 65, totalTrades: 28, period: '2M' }
    }
  ];



  const [checkedItems, setCheckedItems] = useState<{[key: string]: boolean}>({
    'create-agent': false,
    'first-scan': false,
    'first-backtest': false,
    'portfolio-setup': false,
    'discover-agents': false,
    'make-agent-public': false,
    'setup-marketplace': false
  });

  // Load user data on component mount
  useEffect(() => {
    loadUserData();
  }, []);

  // Reload user data when user progress changes (with stable dependencies)
  const progressDeps = useMemo(() => [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ], [
    userProgress.hasCompletedFirstBacktest,
    userProgress.hasCompletedFirstScan,
    userProgress.hasCreatedFirstPortfolio,
    userProgress.hasVisitedDiscoverPage,
    userProgress.hasCreatedFirstPublicAgent,
    userProgress.scansCompleted,
    userProgress.stocksScanned,
    userProgress.backtestsCompleted,
    userProgress.portfoliosCreated
  ]);

  useEffect(() => {
    loadUserData();
  }, progressDeps);

  // Update checked items based on real progress
  useEffect(() => {
    setCheckedItems({
      'create-agent': userStats.agentsCreated > 0,
      'first-scan': userProgress.scansCompleted > 0,
      'first-backtest': userProgress.backtestsCompleted > 0,
      'portfolio-setup': userProgress.portfoliosCreated > 0,
      'discover-agents': userProgress.hasVisitedDiscoverPage,
      'make-agent-public': userProgress.hasCreatedFirstPublicAgent,
      'setup-marketplace': false // This will be updated when user sets up marketplace
    });
  }, [userStats, userProgress]);

  const loadUserData = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Load agent statistics
      const { data: agents } = await supabase
        .from('agents')
        .select('id, created_at')
        .eq('user_id', user.id);

      // Calculate time saved
      const portfolioCount = userProgress.portfoliosCreated;
      const scanTimeMinutes = userProgress.stocksScanned * 1;
      const backtestTimeMinutes = userProgress.backtestsCompleted * 60;
      const portfolioTimeMinutes = portfolioCount * 30;

      const totalTimeMinutes = scanTimeMinutes + backtestTimeMinutes + portfolioTimeMinutes;
      const timeSavedHours = Math.round(totalTimeMinutes / 60 * 10) / 10;

      // Get last activity date from agents
      const lastActivityDate = agents?.length > 0
        ? new Date(Math.max(...agents.map((a: any) => new Date(a.created_at).getTime()))).toISOString()
        : null;

      setUserStats({
        agentsCreated: agents?.length || 0,
        scansCompleted: userProgress.scansCompleted,
        backtestsCompleted: userProgress.backtestsCompleted,
        portfoliosCreated: userProgress.portfoliosCreated,
        winRate: 0,
        totalTrades: userProgress.tradesExecuted,
        successfulTrades: 0,
        timeSavedHours,
        lastActivityDate
      });
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Progress steps for get started journey
  const progressSteps: ProgressStep[] = [
    {
      id: 'create-agent',
      title: 'Create Agent',
      description: 'Build your first AI trading agent',
      icon: <Zap className="w-4 h-4" />,
      action: () => navigate('/agent-builder'),
      completed: checkedItems['create-agent']
    },
    {
      id: 'first-scan',
      title: 'Run Scan',
      description: 'Discover trading opportunities',
      icon: <Target className="w-4 h-4" />,
      action: () => navigate('/scanner'),
      completed: checkedItems['first-scan']
    },
    {
      id: 'first-backtest',
      title: 'Backtest Strategy',
      description: 'Test against historical data',
      icon: <BarChart3 className="w-4 h-4" />,
      action: () => navigate('/backtesting'),
      completed: checkedItems['first-backtest']
    },
    {
      id: 'discover-agents',
      title: 'Explore Marketplace',
      description: 'Discover community agents',
      icon: <Activity className="w-4 h-4" />,
      action: () => navigate('/marketplace'),
      completed: checkedItems['discover-agents']
    }
  ];

  const completedSteps = progressSteps.filter(step => step.completed).length;

  // Load user name
  useEffect(() => {
    const loadUserName = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user?.user_metadata?.full_name) {
          const firstName = user.user_metadata.full_name.split(' ')[0];
          setUserName(firstName);
        }
      } catch (error) {
        console.error('Error loading user name:', error);
      }
    };
    loadUserName();
  }, []);

  // Load watchlist
  useEffect(() => {
    fetchWatchlist();
  }, []);



  if (isLoading) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-white/60">Loading your headquarters...</p>
        </div>
      </div>
    );
  }











  // Simple Clean Earnings Card
  const renderSimpleEarningsCard = () => (
    <div className="h-full flex flex-col justify-center items-center text-center p-6">
      <div className="mb-4">
        <DollarSign className="w-8 h-8 text-gray-700 mx-auto mb-3" />
        <h3 className="text-lg font-bold text-gray-900 mb-1">Total Earnings</h3>
        <p className="text-gray-500 text-sm">This month</p>
      </div>

      <div className="mb-6">
        <div className="text-3xl font-bold text-gray-900 mb-2">$12,847</div>
        <div className="text-green-600 text-sm font-semibold">+18.4% from last month</div>
      </div>

      <div className="w-full grid grid-cols-2 gap-4">
        <div className="text-center">
          <div className="text-lg font-bold text-gray-900">23</div>
          <div className="text-gray-500 text-xs">Sales</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-bold text-gray-900">$8.2k</div>
          <div className="text-gray-500 text-xs">Available</div>
        </div>
      </div>
    </div>
  );





  // Enhanced Watchlist Card with 24h Charts
  const renderEnhancedWatchlistCard = () => {
    // Sample watchlist data with 24h performance
    const sampleWatchlist = [
      { symbol: 'AAPL', name: 'Apple Inc.', price: 175.43, change: 2.34, isPositive: true },
      { symbol: 'TSLA', name: 'Tesla Inc.', price: 248.87, change: -1.23, isPositive: false },
      { symbol: 'NVDA', name: 'NVIDIA Corp.', price: 421.56, change: 5.67, isPositive: true },
      { symbol: 'MSFT', name: 'Microsoft Corp.', price: 378.92, change: 1.45, isPositive: true },
      { symbol: 'GOOGL', name: 'Alphabet Inc.', price: 142.38, change: -0.87, isPositive: false }
    ];

    const displayStocks = watchlistStocks.length > 0 ? watchlistStocks.slice(0, 5) : sampleWatchlist;

    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-bold text-white mb-1">Watchlist</h3>
            <p className="text-white/50 text-sm">24h Performance</p>
          </div>
          <button
            onClick={() => navigate('/stock-screener')}
            className="text-white/60 hover:text-white text-xs font-medium transition-all duration-200"
          >
            Scanner →
          </button>
        </div>

        <div className="flex-1 space-y-3">
          {displayStocks.map((stock, index) => {
            const stockData = sampleWatchlist[index] || stock;

            return (
              <div
                key={stock.symbol || stockData.symbol}
                className="bg-[#0A0A0A]/40 border border-white/[0.06] hover:bg-[#0A0A0A]/60 hover:border-white/[0.12] transition-all duration-300 rounded-xl p-4 cursor-pointer"
                onClick={() => navigate(`/stock-screener/${stock.symbol || stockData.symbol}`)}
              >
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <div className="font-bold text-white text-sm">{stock.symbol || stockData.symbol}</div>
                    <div className="text-white/50 text-xs">{stock.name || stockData.name}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-white text-sm font-medium">${stockData.price}</div>
                    <div className={`text-xs font-medium ${stockData.isPositive ? 'text-green-400' : 'text-red-400'}`}>
                      {stockData.isPositive ? '+' : ''}{stockData.change}%
                    </div>
                  </div>
                </div>

                {/* 24h Line Chart */}
                <div className="w-full h-8">
                  <svg width="100%" height="100%" viewBox="0 0 120 32" preserveAspectRatio="none">
                    <path
                      d={`M0,16 ${Array.from({length: 24}, (_, i) => {
                        const x = (i / 23) * 120;
                        const baseY = 16;
                        const trend = stockData.isPositive ? -1 : 1;
                        const hourlyVariation = Math.sin(i * 0.5) * 3;
                        const trendEffect = (i / 23) * trend * 6;
                        const y = baseY + trendEffect + hourlyVariation;
                        return `L${x},${Math.max(4, Math.min(28, y))}`;
                      }).join(' ')}`}
                      fill="none"
                      stroke={stockData.isPositive ? '#10b981' : '#ef4444'}
                      strokeWidth="1.5"
                      opacity="0.7"
                    />
                  </svg>
                </div>
              </div>
            );
          })}
        </div>

        {watchlistStocks.length === 0 && (
          <div className="mt-4 text-center">
            <p className="text-white/40 text-xs">Showing sample data</p>
          </div>
        )}
      </div>
    );
  };

  // Clean Row-Based Agents Card
  const renderCleanRowAgentsCard = () => {
    // Sort agents by win rate (highest first)
    const sortedAgents = [...mockAgents].sort((a, b) => b.winRate - a.winRate);

    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-bold text-white mb-1">Your Agents</h3>
            <p className="text-white/50 text-xs">AI trading strategies</p>
          </div>
          <div className="text-right">
            <div className="text-green-400 text-lg font-bold">+12.4%</div>
            <div className="text-white/50 text-xs">Total Return</div>
          </div>
        </div>

        {/* Agents List - Clean Rows */}
        <div className="flex-1 space-y-2">
          {sortedAgents.map((agent, index) => (
            <div
              key={agent.id}
              className="flex items-center justify-between py-3 px-4 bg-white/[0.02] rounded-lg border border-white/[0.05] hover:bg-white/[0.04] hover:border-white/[0.08] transition-all duration-200 cursor-pointer"
              onClick={() => navigate('/builder')}
            >
              {/* Left Side - Rank & Name */}
              <div className="flex items-center gap-4">
                <div className="text-white font-bold text-lg min-w-[24px]">#{index + 1}</div>
                <div>
                  <div className="font-semibold text-white text-sm">{agent.name}</div>
                  <div className="text-white/50 text-xs">{agent.description}</div>
                </div>
              </div>

              {/* Right Side - Stats */}
              <div className="flex items-center gap-6 text-right">
                <div>
                  <div className="text-white font-semibold text-sm">{agent.winRate}%</div>
                  <div className="text-white/40 text-xs">Win Rate</div>
                </div>
                <div>
                  <div className={`text-sm font-bold ${
                    agent.returnPercentage >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {agent.returnPercentage >= 0 ? '+' : ''}{agent.returnPercentage}%
                  </div>
                  <div className="text-white/40 text-xs">Return</div>
                </div>
                <div>
                  <div className="text-white font-semibold text-sm">{agent.totalTrades}</div>
                  <div className="text-white/40 text-xs">Trades</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {mockAgents.length === 0 && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Activity className="w-8 h-8 text-white/30 mx-auto mb-3" />
              <h4 className="text-white/60 text-base font-medium mb-2">No agents created yet</h4>
              <p className="text-white/40 text-sm mb-4">Create your first AI trading agent</p>
              <button
                onClick={() => navigate('/builder')}
                className="bg-white/[0.05] border border-white/[0.10] text-white text-sm font-medium py-2 px-4 rounded-lg hover:bg-white/[0.08] hover:border-white/[0.15] transition-all duration-200"
              >
                Create Agent
              </button>
            </div>
          </div>
        )}

        {/* Action Button */}
        <button
          onClick={() => navigate('/builder')}
          className="w-full mt-4 py-2 text-white/70 hover:text-white text-sm font-medium transition-all duration-200"
        >
          Manage All Agents
        </button>
      </div>
    );
  };



  // Market Overview Card
  const renderMarketOverviewCard = () => (
    <div className="h-full flex flex-col">
      <div className="mb-6">
        <h3 className="text-lg font-bold text-white mb-1">Market Overview</h3>
        <p className="text-white/50 text-sm">Today's performance</p>
      </div>

      <div className="flex-1 space-y-4">
        {/* Market Indices */}
        <div className="space-y-3">
          {[
            { name: 'S&P 500', value: '4,567.89', change: '+1.2%', positive: true },
            { name: 'NASDAQ', value: '14,234.56', change: '+0.8%', positive: true },
            { name: 'DOW', value: '34,567.12', change: '-0.3%', positive: false }
          ].map((index) => (
            <div key={index.name} className="flex items-center justify-between py-2 px-3 bg-white/[0.02] rounded-lg border border-white/[0.05]">
              <div>
                <div className="font-semibold text-white text-sm">{index.name}</div>
                <div className="text-white/50 text-xs">{index.value}</div>
              </div>
              <div className={`text-sm font-medium ${index.positive ? 'text-green-400' : 'text-red-400'}`}>
                {index.change}
              </div>
            </div>
          ))}
        </div>

        {/* Market Status */}
        <div className="mt-auto pt-4 border-t border-white/[0.05]">
          <div className="flex items-center justify-between">
            <span className="text-white/50 text-sm">Market Status</span>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-green-400 text-sm font-medium">Open</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      {/* Clean Header */}
      <div className="px-8 py-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2 font-mono">
              Welcome to HQ, <span className="text-white">{userName}</span>
            </h1>
            <p className="text-white/50 text-lg font-sans">Your premium trading command center</p>
          </div>

          {/* Setup Progress - Right Side */}
          <div className="text-right">
            <div className="mb-3">
              <div className="text-2xl font-bold text-green-400">
                {Math.round((completedSteps / progressSteps.length) * 100)}%
              </div>
              <div className="text-white/40 text-sm">Setup Complete</div>
            </div>

            {/* Mini Progress Bar */}
            <div className="relative w-32 h-1.5 bg-white/[0.08] rounded-full overflow-hidden">
              <motion.div
                className="absolute top-0 left-0 h-full bg-green-400 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(completedSteps / progressSteps.length) * 100}%` }}
                transition={{ duration: 1.5, ease: "easeOut" }}
              />
            </div>
          </div>
        </div>
      </div>



      {/* Premium Bento Dashboard */}
      <div className="p-8">
        <div className="max-w-7xl mx-auto">
          {/* Clean Bento Grid Layout */}
          <div className="grid grid-cols-4 grid-rows-2 gap-6 h-[600px]">
            {/* Earnings Card - Clean White Standout */}
            <div className="col-span-1 row-span-1 bg-white rounded-2xl shadow-xl border border-gray-100 transition-all duration-300 hover:shadow-2xl hover:scale-[1.02]">
              {renderSimpleEarningsCard()}
            </div>

            {/* Watchlist Card - With Charts */}
            <div className="col-span-1 row-span-2 bg-[#0F0F0F] backdrop-blur-sm border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-[#111111] hover:border-white/[0.12] shadow-2xl">
              {renderEnhancedWatchlistCard()}
            </div>

            {/* Your Agents Card - Big Rectangle */}
            <div className="col-span-2 row-span-2 bg-[#0F0F0F] backdrop-blur-sm border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-[#111111] hover:border-white/[0.12] shadow-2xl">
              {renderCleanRowAgentsCard()}
            </div>

            {/* Market Overview Card */}
            <div className="col-span-1 row-span-1 bg-[#0F0F0F] backdrop-blur-sm border border-white/[0.08] rounded-2xl p-6 transition-all duration-300 hover:bg-[#111111] hover:border-white/[0.12] shadow-2xl">
              {renderMarketOverviewCard()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
