import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import TradingChart from '@/components/TradingChart';
import { ArrowLeft, TrendingUp, Target, Clock } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { useToast } from '@/components/ui/use-toast';

interface ScanContext {
  result: {
    symbol: string;
    signal: string;
    confidence: number;
    price?: number;
    change?: number;
    percentChange?: number;
  };
  agent: {
    id: string;
    name: string;
  };
  market: {
    value: string;
    label: string;
  };
  scanTimestamp: string; 
}

const Screener: React.FC = () => {
  const { symbol } = useParams<{ symbol?: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { isInWatchlist, addToWatchlist, removeFromWatchlist } = useWatchlist();

  const displaySymbol = symbol || 'TSLA';
  const [stockName, setStockName] = useState<string>('');
  const [isStarred, setIsStarred] = useState(false);

  const scanContext = location.state?.scanContext as ScanContext | undefined;

  // Update starred state when symbol changes
  useEffect(() => {
    setIsStarred(isInWatchlist(displaySymbol));
    // Set a default stock name - in a real app, you'd fetch this from an API
    setStockName(getStockName(displaySymbol));
  }, [displaySymbol, isInWatchlist]);

  // Helper function to get stock name (mock data)
  const getStockName = (symbol: string): string => {
    const stockNames: { [key: string]: string } = {
      'AAPL': 'Apple Inc.',
      'MSFT': 'Microsoft Corporation',
      'GOOGL': 'Alphabet Inc.',
      'AMZN': 'Amazon.com Inc.',
      'TSLA': 'Tesla Inc.',
      'META': 'Meta Platforms Inc.',
      'NVDA': 'NVIDIA Corporation',
      'NFLX': 'Netflix Inc.',
      'AMD': 'Advanced Micro Devices Inc.',
      'INTC': 'Intel Corporation'
    };
    return stockNames[symbol] || `${symbol} Inc.`;
  };

  // Handle star toggle
  const handleStarToggle = async () => {
    try {
      if (isStarred) {
        const success = await removeFromWatchlist(displaySymbol);
        if (success) {
          setIsStarred(false);
          toast({
            title: 'Removed from Watchlist',
            description: `${displaySymbol} has been removed from your watchlist.`,
          });
        }
      } else {
        const success = await addToWatchlist({ symbol: displaySymbol, name: stockName });
        if (success) {
          setIsStarred(true);
          toast({
            title: 'Added to Watchlist',
            description: `${displaySymbol} has been added to your watchlist.`,
          });
        }
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update watchlist. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const scanTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - scanTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className="h-full bg-[#0A0A0A] overflow-hidden flex flex-col">
      {/* Agent Scan Context Header */}
      {scanContext && (
        <div className="bg-gradient-to-r from-[#141414] to-[#0A0A0A] border-b border-white/[0.06] p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/agent-scanner')}
                className="flex items-center gap-2 text-white/60 hover:text-white transition-colors duration-200 group"
              >
                <ArrowLeft className="h-4 w-4 group-hover:-translate-x-0.5 transition-transform duration-200" />
                <span className="text-sm">Back to Scanner</span>
              </button>

              <div className="h-4 w-px bg-white/20" />

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-emerald-500/20 to-emerald-600/20 border border-emerald-500/30 flex items-center justify-center">
                  <Target className="h-4 w-4 text-emerald-400" />
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <h2 className="text-white font-semibold">{displaySymbol}</h2>
                    <button
                      onClick={handleStarToggle}
                      className={`w-6 h-6 rounded flex items-center justify-center transition-all duration-200 ${
                        isStarred
                          ? 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30'
                          : 'bg-white/[0.05] text-white/40 hover:bg-white/[0.1] hover:text-white/60'
                      }`}
                      title={isStarred ? 'Remove from watchlist' : 'Add to watchlist'}
                    >
                      <span className="text-xs">{isStarred ? '⭐' : '☆'}</span>
                    </button>
                    <div className={`px-2 py-1 rounded-md text-xs font-medium border ${
                      scanContext.result.confidence >= 80
                        ? 'bg-emerald-500/20 text-emerald-300 border-emerald-500/40'
                        : scanContext.result.confidence >= 60
                        ? 'bg-yellow-500/20 text-yellow-300 border-yellow-500/40'
                        : 'bg-orange-500/20 text-orange-300 border-orange-500/40'
                    }`}>
                      {scanContext.result.confidence}% CONFIDENCE
                    </div>
                    <div className={`px-2 py-1 rounded-md text-xs font-medium border ${
                      scanContext.result.signal === 'bullish'
                        ? 'bg-emerald-600/30 text-emerald-300 border-emerald-500/50'
                        : 'bg-red-600/30 text-red-300 border-red-500/50'
                    }`}>
                      {scanContext.result.signal.toUpperCase()}
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-white/60 mt-1">
                    <span>Detected by {scanContext.agent.name}</span>
                    <span>•</span>
                    <span>{scanContext.market.label}</span>
                    <span>•</span>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{formatTimeAgo(scanContext.scanTimestamp)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Price info if available */}
            {scanContext.result.price && (
              <div className="flex items-center gap-4">
                <div className="text-right">
                  <div className="text-lg font-bold text-white">
                    ${scanContext.result.price.toFixed(2)}
                  </div>
                  {scanContext.result.change !== undefined && (
                    <div className={`text-sm font-medium ${
                      scanContext.result.change >= 0 ? 'text-emerald-400' : 'text-red-400'
                    }`}>
                      {scanContext.result.change >= 0 ? '+' : ''}${scanContext.result.change.toFixed(2)}
                      {scanContext.result.percentChange !== undefined && (
                        <span className="ml-1">
                          ({scanContext.result.percentChange.toFixed(2)}%)
                        </span>
                      )}
                    </div>
                  )}
                </div>
                <div className={`w-10 h-10 rounded-full flex items-center justify-center backdrop-blur-sm border ${
                  (scanContext.result.change || 0) >= 0
                    ? 'bg-emerald-500/20 border-emerald-500/40'
                    : 'bg-red-500/20 border-red-500/40'
                }`}>
                  <TrendingUp className={`h-5 w-5 ${
                    (scanContext.result.change || 0) >= 0 ? 'text-emerald-400' : 'text-red-400 rotate-180'
                  }`} />
                </div>

                {/* Star Icon integrated into existing design */}
                <button
                  onClick={handleStarToggle}
                  className={`w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200 ${
                    isStarred
                      ? 'bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30'
                      : 'bg-white/[0.05] text-white/40 hover:bg-white/[0.1] hover:text-white/60'
                  }`}
                  title={isStarred ? 'Remove from watchlist' : 'Add to watchlist'}
                >
                  <span className="text-sm">{isStarred ? '⭐' : '☆'}</span>
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Trading Chart */}
      <div className="flex-1 w-full">
        <TradingChart symbol={displaySymbol} />
      </div>
    </div>
  );
};

export default Screener;
